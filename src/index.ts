interface Env {
	iOS_APPS_STORE_PH: KVNamespace;
	API_KEY: string;
	KEYWORDS: string;
	CATEGORIES: string; // 逗号分隔的分类ID列表
	API_BASE_URL: string; // 基础API URL
	SEARCH_TERMS: string; // 搜索关键词，逗号分隔
	SEARCH_API_URL: string; // 搜索API URL
}

interface AppItem {
	id: string;
	title: string;
}

interface SearchAppItem {
	id: number;
	title: string;
}

type ApiResponse = AppItem[];
type SearchApiResponse = SearchAppItem[];

// Fetch with timeout to avoid hanging requests
const DEFAULT_TIMEOUT_MS = 30000; // 增加超时时间到30秒
async function withTimeout(url: string, init: RequestInit = {}, ms: number = DEFAULT_TIMEOUT_MS): Promise<Response> {
	const controller = new AbortController();
	const id = setTimeout(() => controller.abort(), ms);
	try {
		return await fetch(url, { ...init, signal: controller.signal });
	} finally {
		clearTimeout(id);
	}
}

// 移除了mapWithConcurrency函数，改为顺序处理


export default {
	async fetch(_request: Request, _env: Env): Promise<Response> {
		return new Response("Use scheduled trigger");
	},

	async scheduled(_event: ScheduledEvent, env: Env): Promise<void> {
		try {
			const allApps = new Map<string, { appid: string; app_name: string; source: string }>();

			// 方法1: 从分类列表获取应用
			await fetchFromCategories(env, allApps);

			// 方法2: 从搜索接口获取应用
			await fetchFromSearch(env, allApps);

			// 存储所有去重后的应用
			const batch = Array.from(allApps.entries()).map(([key, value]) => ({
				key,
				value: JSON.stringify(value)
			}));

			await writeToKV(env.iOS_APPS_STORE_PH, batch);
			console.log(`总共处理了 ${allApps.size} 个唯一应用`);

		} catch (error) {
			console.error(`Error during scheduled task: ${error instanceof Error ? error.message : String(error)}`);
		}
	}

};

// Delay helper
function delay(ms: number) {
	return new Promise<void>(resolve => setTimeout(resolve, ms));
}

// Fetch with retry and exponential backoff; retries on 5xx/429 or network errors
async function fetchWithRetry(url: string, init: RequestInit = {}, attempts = 3, baseDelayMs = 500): Promise<Response> {
	for (let i = 0; i < attempts; i++) {
		try {
			const res = await withTimeout(url, init, DEFAULT_TIMEOUT_MS);
			if (res.ok) return res;
			// Retry for 5xx and 429
			if (res.status >= 500 || res.status === 429) {
				const backoff = baseDelayMs * Math.pow(2, i) + Math.floor(Math.random() * 100);
				await delay(backoff);
				continue;
			}
			return res;
		} catch (err) {
			const backoff = baseDelayMs * Math.pow(2, i) + Math.floor(Math.random() * 100);
			await delay(backoff);
		}
	}
	// Final attempt without catching to surface the error
	return await withTimeout(url, init, DEFAULT_TIMEOUT_MS);
}

// 从分类列表获取应用
async function fetchFromCategories(env: Env, allApps: Map<string, { appid: string; app_name: string; source: string }>) {
	const categories = env.CATEGORIES.split(',');
	const keywords = env.KEYWORDS.split(',').map(k => k.toLowerCase().trim());

	// 改为顺序处理，不使用并发
	for (const category of categories) {
		try {
			const apiUrl = `${env.API_BASE_URL}?category=${category}&country=ph`;
			const response = await fetchWithRetry(apiUrl, { headers: { "x-api-key": env.API_KEY } });

			if (!response.ok) {
				let errText = '';
				try { errText = await response.text(); } catch {}
				console.log(`分类 ${category} 请求失败: ${response.status}${errText ? ' - ' + errText.slice(0, 500) : ''}`);
				continue;
			}

			const data: ApiResponse = await response.json();
			if (!Array.isArray(data)) {
				console.log(`分类 ${category} 响应格式错误`);
				continue;
			}

			const matchedApps = data.filter(app => {
				const appTitle = app.title.toLowerCase();
				return keywords.some(keyword => {
					// 支持短句搜索：如果关键字包含空格，则作为短句匹配
					if (keyword.includes(' ')) {
						return appTitle.includes(keyword);
					}
					// 单词匹配：确保匹配完整单词边界
					const wordRegex = new RegExp(`\\b${keyword}\\b`, 'i');
					return wordRegex.test(appTitle) || appTitle.includes(keyword);
				});
			});

			// 添加到总集合中
			matchedApps.forEach(app => {
				allApps.set(app.id, {
					appid: app.id,
					app_name: app.title,
					source: `category-${category}`
				});
			});

			console.log(`分类 ${category}: 匹配到 ${matchedApps.length} 个应用`);
		} catch (error) {
			console.error(`分类 ${category} 处理失败:`, error instanceof Error ? error.message : String(error));
		} finally {
			await delay(10000);
		}
	}
}

// 从搜索接口获取应用
async function fetchFromSearch(env: Env, allApps: Map<string, { appid: string; app_name: string; source: string }>) {
	if (!env.SEARCH_TERMS || !env.SEARCH_API_URL) {
		console.log('搜索功能未配置，跳过搜索');
		return;
	}

	const searchTerms = env.SEARCH_TERMS.split(',').map(term => term.trim());
	const keywords = env.KEYWORDS.split(',').map(k => k.toLowerCase().trim());

	// 改为顺序处理，不使用并发
	for (const term of searchTerms) {
		try {
			const searchUrl = `${env.SEARCH_API_URL}?term=${encodeURIComponent(term)}&num=100&country=ph&lang=en`;
			const response = await fetchWithRetry(searchUrl, { headers: { "x-api-key": env.API_KEY } });

			if (!response.ok) {
				let errText = '';
				try { errText = await response.text(); } catch {}
				console.log(`搜索词 "${term}" 请求失败: ${response.status}${errText ? ' - ' + errText.slice(0, 500) : ''}`);
				continue;
			}

			const data: SearchApiResponse = await response.json();
			if (!Array.isArray(data)) {
				console.log(`搜索词 "${term}" 响应格式错误`);
				continue;
			}

			// 过滤搜索结果，只保留与关键字匹配的应用
			const matchedApps = data.filter(app => {
				const appTitle = app.title.toLowerCase();
				return keywords.some(keyword => {
					// 支持短句搜索：如果关键字包含空格，则作为短句匹配
					if (keyword.includes(' ')) {
						return appTitle.includes(keyword);
					}
					// 单词匹配：确保匹配完整单词边界
					const wordRegex = new RegExp(`\\b${keyword}\\b`, 'i');
					return wordRegex.test(appTitle) || appTitle.includes(keyword);
				});
			});

			// 添加到总集合中
			matchedApps.forEach(app => {
				allApps.set(app.id.toString(), {
					appid: app.id.toString(),
					app_name: app.title,
					source: `search-${term}`
				});
			});

			console.log(`搜索词 "${term}": 找到 ${data.length} 个应用，匹配到 ${matchedApps.length} 个应用`);
		} catch (error) {
			console.error(`搜索词 "${term}" 处理失败:`, error instanceof Error ? error.message : String(error));
		} finally {
			await delay(10000);
		}
	}
}

// 解决方案1: 使用 Map 去重，避免重复检查
async function writeToKV(kv: KVNamespace, items: { key: string, value: string }[]) {
	// 先去重，避免同一批次中的重复 key
	const uniqueItems = new Map<string, string>();
	items.forEach(({ key, value }) => {
		uniqueItems.set(key, value);
	});

	// 分批检查与写入，避免过高并发
	const keys = Array.from(uniqueItems.keys());
	const BATCH_SIZE = 50;
	let totalNew = 0;
	for (let i = 0; i < keys.length; i += BATCH_SIZE) {
		const slice = keys.slice(i, i + BATCH_SIZE);
		const checks = await Promise.all(
			slice.map(async (key) => ({ key, exists: (await kv.get(key)) !== null }))
		);
		const toPut = checks
			.filter(({ exists }) => !exists)
			.map(({ key }) => ({ key, value: uniqueItems.get(key)! }));
		if (toPut.length > 0) {
			await Promise.all(toPut.map(({ key, value }) => kv.put(key, value)));
			totalNew += toPut.length;
		}
	}

	console.log(`Processed ${items.length} items (${uniqueItems.size} unique), added ${totalNew} new items`);
}


