/**
 * For more details on how to configure Wrangler, refer to:
 * https://developers.cloudflare.com/workers/wrangler/configuration/
 */
{
	"$schema": "node_modules/wrangler/config-schema.json",
	"name": "app-store-list-auto-scraper",
	"main": "src/index.ts",
	"compatibility_date": "2025-08-05",
	"observability": {
		"enabled": true
	},
	"triggers": {
		"crons": [
			"0 * * * *"
		]
	},
	"kv_namespaces": [
		{
			"binding": "iOS_APPS_STORE_PH",
			"id": "298fd249e669431f8c4a448ee475dbe2"
		}
	],
	"vars": {
		"KEYWORDS": "cash,loan,peso,pera,credit,lending,mabilis",
		"CATEGORIES": "6015,6000,6002,6012",
		"API_BASE_URL": "https://appleapi.yuandao.world/list/topfreeapplications",
		"SEARCH_TERMS": "loan app philippines",
		"SEARCH_API_URL": "https://appleapi.yuandao.world/search"
	},
	/**
	 * Smart Placement
	 * Docs: https://developers.cloudflare.com/workers/configuration/smart-placement/#smart-placement
	 */
	// "placement": { "mode": "smart" },

	/**
	 * Bindings
	 * Bindings allow your Worker to interact with resources on the Cloudflare Developer Platform, including
	 * databases, object storage, AI inference, real-time communication and more.
	 * https://developers.cloudflare.com/workers/runtime-apis/bindings/
	 */

	/**
	 * Environment Variables
	 * https://developers.cloudflare.com/workers/wrangler/configuration/#environment-variables
	 */
	// "vars": { "MY_VARIABLE": "production_value" },
	/**
	 * Note: Use secrets to store sensitive data.
	 * https://developers.cloudflare.com/workers/configuration/secrets/
	 */

	/**
	 * Static Assets
	 * https://developers.cloudflare.com/workers/static-assets/binding/
	 */
	// "assets": { "directory": "./public/", "binding": "ASSETS" },

	/**
	 * Service Bindings (communicate between multiple Workers)
	 * https://developers.cloudflare.com/workers/wrangler/configuration/#service-bindings
	 */
	// "services": [{ "binding": "MY_SERVICE", "service": "my-service" }]
}
