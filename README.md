# App Store List Auto Scraper

A Cloudflare Workers-based scraper that collects iOS App Store apps for the Philippines via two data sources:
- Category listing API (top free applications by category)
- Keyword search API (search by terms/phrases like "loan app philippines")

Results are de-duplicated and stored in Cloudflare KV.

## Features
- Dual data sources: category list + keyword search
- Configurable keywords, categories, and search terms
- Case-insensitive matching, supports phrases (e.g., "loan app")
- De-duplication before K<PERSON> writes
- Safe error handling and concise logs

## Architecture
- Cloudflare Worker with a scheduled cron trigger (hourly by default)
- KV namespace for persistence: `iOS_APPS_STORE_PH`
- API key provided via encrypted Cloudflare secret `API_KEY`

## Data sources
1) Category list API
   - Base URL: `API_BASE_URL` (wrangler.jsonc)
   - Request: `GET ${API_BASE_URL}?category={CATEGORY_ID}&country=ph`
   - Response (simplified): `[{ id: string, title: string }]`
   - Filtering: app title contains any configured `KEYWORDS`

2) Search API
   - Base URL: `SEARCH_API_URL` (wrangler.jsonc)
   - Request: `GET ${SEARCH_API_URL}?term={TERM}&num=100&country=ph&lang=en`
   - Response: array of rich app objects (see `SearchAppItem` in `src/index.ts`)
   - Terms: comma-separated `SEARCH_TERMS` (supports phrases)

## Stored KV schema
Each saved KV item:
```
key: app.id (string)
value: {
  "appid": string,
  "app_name": string,
  "source": "category-{id}" | "search-{term}"
}
```

## Configuration
Configuration lives in `wrangler.jsonc` under `vars`:
- `KEYWORDS`: comma-separated keywords for category list filtering (case-insensitive)
- `CATEGORIES`: comma-separated Apple category IDs (e.g., 6015, 6000, 6002, 6012)
- `API_BASE_URL`: category list API base URL
- `SEARCH_TERMS`: comma-separated search terms/phrases (e.g., "loan app philippines,loan app,credit app")
- `SEARCH_API_URL`: search API base URL

KV binding is declared in `wrangler.jsonc`:
```
"kv_namespaces": [
  { "binding": "iOS_APPS_STORE_PH", "id": "<your-kv-id>" }
]
```

## Secrets (API key)
Do NOT hardcode secrets in the repo. Use Cloudflare Encrypted Variables.

Set secret via Cloudflare Dashboard:
- Workers & Pages → your Worker → Settings → Variables → Add variable
- Name: `API_KEY` (Encrypt)

Or via CLI:
```
wrangler secret put API_KEY
```
Follow the prompt to paste the value.

### Local development secret
For local development, you can create a `.dev.vars` file (do not commit this file):
```
API_KEY=your-api-key-here
```

## Running locally
Requires Node.js and Wrangler.

Install deps:
```
npm install
```

Start dev server and trigger the scheduled task once at startup:
```
npx wrangler dev --test-scheduled
```
Notes:
- The `fetch` handler returns a simple message; the main logic runs in the `scheduled` handler.
- Cron triggers run hourly in production; `--test-scheduled` simulates a run locally.

## Deploying
```
npx wrangler deploy
```
Make sure:
- KV namespace binding `iOS_APPS_STORE_PH` exists and matches the ID in `wrangler.jsonc`
- Secret `API_KEY` is set in Cloudflare

## Customization
- Adjust `KEYWORDS` for category-based filtering
- Adjust `CATEGORIES` to target different Apple categories
- Adjust `SEARCH_TERMS` to broaden or narrow search coverage (phrases supported)

## Logs & troubleshooting
- Category list request errors are logged per category
- Search request errors are logged per term
- All errors are caught at the scheduled task level with a safe message
- KV writes are de-duplicated; summary counts are logged

## Project structure
```
src/
  index.ts        # Worker code (fetchFromCategories, fetchFromSearch, writeToKV)
wrangler.jsonc    # Worker config (cron, KV binding, vars)
tsconfig.json     # TypeScript config
package.json      # Dependencies and scripts
```

## License
Private/internal project unless you specify otherwise.

